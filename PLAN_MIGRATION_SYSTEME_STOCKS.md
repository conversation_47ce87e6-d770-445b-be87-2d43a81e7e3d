# 📋 Plan de Migration Complet - Système de Stocks Simplifié

## 🎯 Objectif
Migrer de l'ancien système de recettes vers le nouveau système simplifié **MENU + INGRÉDIENTS** avec gestion de stocks automatisée, et intégrer cette logique dans toutes les interfaces (Bar, Restaurant, Inventaire).

---

## 🗂️ Phase 1: Nettoyage de l'Ancien Système

### 🔴 Backend - Suppression du Code Obsolète

#### **1.1 Services à Supprimer/Modifier**
- [x] **Supprimer** : `backend/services/recette.service.js`
- [x] **Modifier** : `backend/services/posStockIntegration.service.js`
  - Remplacer les références `recette_id` par `produit_id`
  - Adapter les requêtes SQL pour utiliser `ProduitsIngredients`
- [x] **Modifier** : `backend/services/inventaire.service.js`
  - Supprimer les méthodes liées aux recettes
  - Garder uniquement la gestion des ingrédients

#### **1.2 Contrôleurs à Supprimer/Modifier**
- [x] **Supprimer** : `backend/controllers/recette.controller.js`
- [ ] **Modifier** : `backend/controllers/posStock.controller.js`
  - Adapter pour utiliser les produits au lieu des recettes
- [ ] **Modifier** : `backend/controllers/inventaire.controller.js`
  - Supprimer les endpoints de recettes

#### **1.3 Routes à Supprimer/Modifier**
- [x] **Supprimer** : `backend/routes/recette.routes.js`
- [x] **Modifier** : `backend/routes/index.js`
  - Supprimer la ligne `const recetteRoutes = require('./recette.routes');`
  - Supprimer la ligne `router.use('/recettes', verifyToken, recetteRoutes);`
- [ ] **Modifier** : `backend/routes/posStock.routes.js`
  - Adapter les routes pour les produits

#### **1.4 Base de Données - Tables Obsolètes**
- [x] **Créer migration** : `backend/schema/migrations/cleanup_old_recipe_system.sql`
  ```sql
  -- Sauvegarder les données importantes si nécessaire
  -- Puis supprimer les tables obsolètes
  DROP TABLE IF EXISTS "RecettesIngredients";
  DROP TABLE IF EXISTS "Recettes";
  DROP VIEW IF EXISTS "VueRecettesCouts";
  
  -- Supprimer les colonnes obsolètes
  ALTER TABLE "Produits" DROP COLUMN IF EXISTS "recette_id";
  ALTER TABLE "MouvementsStock" DROP COLUMN IF EXISTS "recette_id";
  ```

### 🔴 Frontend - Suppression du Code Obsolète

#### **1.5 Services Frontend à Supprimer/Modifier**
- [x] **Supprimer** : `hotel-frontend/src/services/recette.service.ts`
- [x] **Modifier** : `hotel-frontend/src/services/inventaire.service.ts`
  - Supprimer les méthodes liées aux recettes

#### **1.6 Composants Frontend à Supprimer/Modifier**
- [x] **Identifier et supprimer** tous les composants liés aux recettes :
  - Rechercher dans `hotel-frontend/src/components/` les fichiers contenant "recette"
  - Supprimer les pages de gestion des recettes
  - Supprimer les formulaires de création/édition de recettes

#### **1.7 Pages Frontend à Supprimer/Modifier**
- [x] **Supprimer** : Pages de gestion des recettes
- [x] **Modifier** : Pages d'inventaire pour supprimer les références aux recettes

---

## 🟢 Phase 2: Implémentation du Nouveau Système

### **2.1 Nouveaux Services Backend**

#### **2.1.1 Service Produits-Ingrédients**
- [x] **Créé** : `backend/services/produitIngredient.service.js`
- [x] **Créer contrôleur** : `backend/controllers/produitIngredient.controller.js`
- [x] **Créer routes** : `backend/routes/produitIngredient.routes.js`

#### **2.1.2 Service Import Menu avec Ingrédients**
- [x] **Créer** : `backend/services/menuIngredientImport.service.js`
  - Import du menu (produits)
  - Import des liens produits-ingrédients
  - Validation des données
  - Calcul automatique des coûts

#### **2.1.3 Adaptation Services Existants**
- [x] **Modifier** : `backend/services/ingredientImport.service.js`
  - Ajouter l'initialisation automatique des stocks
  - Intégrer avec `StockManagementService`
- [x] **Modifier** : `backend/services/menuImport.service.js`
  - Adapter pour le nouveau système
  - Intégrer avec `ProduitIngredientService`

### **2.2 Nouveaux Services Frontend**

#### **2.2.1 Service Produits-Ingrédients**
- [x] **Créer** : `hotel-frontend/src/services/produitIngredient.service.ts`
  ```typescript
  // Gestion des liens produits-ingrédients
  // CRUD des compositions de plats
  // Calcul des coûts en temps réel
  ```

#### **2.2.2 Service Menu Intégré**
- [x] **Créer** : `hotel-frontend/src/services/menuIntegre.service.ts`
  ```typescript
  // Gestion complète du menu avec stocks
  // Vérification de disponibilité
  // Calcul des marges
  ```

### **2.3 Nouveaux Templates Excel**
- [x] **Créé** : Template produits-ingrédients
- [x] **Créer** : Template menu restaurant avec ingrédients
  - Ajouter colonnes pour les ingrédients
  - Format: nom:quantité:unité,nom2:quantité:unité
- [x] **Créer** : Template carte bar avec ingrédients
  - Intégrer avec le système de stocks boissons

---

## 🔄 Phase 3: Intégration dans les Interfaces

### **3.1 Interface Restaurant**

#### **3.1.1 Page Menu Restaurant**
- [ ] **Modifier** : `hotel-frontend/src/pages/RestaurantMenuPage.tsx`
  - Afficher les coûts de revient
  - Afficher la disponibilité des plats
  - Alertes pour ingrédients manquants
  - Calcul automatique des marges

#### **3.1.2 Composants Restaurant**
- [ ] **Créer** : `hotel-frontend/src/components/restaurant/PlatComposition.tsx`
  ```typescript
  // Affichage de la composition d'un plat
  // Liste des ingrédients avec quantités
  // Statut de disponibilité
  ```
- [ ] **Créer** : `hotel-frontend/src/components/restaurant/MenuStockStatus.tsx`
  ```typescript
  // Vue d'ensemble du menu avec statuts de stock
  // Plats disponibles/indisponibles
  // Alertes de rupture
  ```

#### **3.1.3 Gestion des Commandes Restaurant**
- [ ] **Modifier** : `hotel-frontend/src/components/pos/CommandeForm.tsx`
  - Vérification de disponibilité avant commande
  - Affichage des alertes de stock
  - Déduction automatique lors de la validation

### **3.2 Interface Bar**

#### **3.2.1 Page Carte Bar**
- [ ] **Modifier** : `hotel-frontend/src/pages/BarMenuPage.tsx`
  - Intégrer avec le système de stocks boissons
  - Afficher la disponibilité des cocktails
  - Gestion des ingrédients pour cocktails

#### **3.2.2 Composants Bar**
- [ ] **Créer** : `hotel-frontend/src/components/bar/CocktailComposition.tsx`
  ```typescript
  // Composition des cocktails
  // Ingrédients et quantités
  // Disponibilité des spiritueux
  ```
- [ ] **Créer** : `hotel-frontend/src/components/bar/BarStockStatus.tsx`
  ```typescript
  // Statut des stocks bar
  // Boissons disponibles
  // Alertes de rupture
  ```

### **3.3 Interface Inventaire**

#### **3.3.1 Dashboard Inventaire Unifié**
- [ ] **Créer** : `hotel-frontend/src/pages/InventaireDashboard.tsx`
  ```typescript
  // Vue d'ensemble : Ingrédients + Boissons
  // Alertes de stock unifiées
  // Mouvements récents
  // Rapports de consommation
  ```

#### **3.3.2 Gestion des Stocks**
- [ ] **Modifier** : `hotel-frontend/src/components/ui/StockManager.tsx`
  - Intégrer le nouveau `StockManagementService`
  - Gestion des mouvements manuels
  - Historique des mouvements

#### **3.3.3 Rapports Avancés**
- [ ] **Créer** : `hotel-frontend/src/components/inventaire/RapportConsommation.tsx`
  ```typescript
  // Rapport de consommation par produit
  // Analyse coûts théoriques vs réels
  // Prévisions de commandes
  ```

---

## 🔧 Phase 4: Intégration POS

### **4.1 Modification du Système POS**

#### **4.1.1 Vérification de Disponibilité**
- [ ] **Modifier** : `hotel-frontend/src/components/pos/ProductSelection.tsx`
  - Vérifier la disponibilité avant ajout au panier
  - Afficher les alertes de stock
  - Proposer des alternatives si rupture

#### **4.1.2 Déduction Automatique**
- [ ] **Modifier** : `backend/services/posStockIntegration.service.js`
  - Remplacer `processProductSale` pour utiliser `ProduitsIngredients`
  - Déduction automatique lors de la validation de commande
  - Génération d'alertes en temps réel

### **4.2 Interface de Caisse**
- [ ] **Modifier** : `hotel-frontend/src/components/pos/CaisseInterface.tsx`
  - Afficher les alertes de stock
  - Bloquer les produits indisponibles
  - Afficher les coûts de revient (pour les managers)

---

## 📊 Phase 5: Tableaux de Bord et Rapports

### **5.1 Dashboard Manager**
- [ ] **Créer** : `hotel-frontend/src/pages/ManagerDashboard.tsx`
  ```typescript
  // Vue d'ensemble complète
  // Rentabilité par produit
  // Alertes de stock critiques
  // Suggestions d'optimisation
  ```

### **5.2 Rapports Financiers**
- [ ] **Créer** : `hotel-frontend/src/components/rapports/RapportRentabilite.tsx`
  ```typescript
  // Analyse de rentabilité par produit
  // Évolution des coûts
  // Marges réalisées vs théoriques
  ```

### **5.3 Rapports Opérationnels**
- [ ] **Créer** : `hotel-frontend/src/components/rapports/RapportOperationnel.tsx`
  ```typescript
  // Consommation par service
  // Rotation des stocks
  // Prévisions de commandes
  ```

---

## 🧪 Phase 6: Tests et Validation

### **6.1 Tests Backend**
- [ ] **Créer** : Tests unitaires pour `ProduitIngredientService`
- [ ] **Créer** : Tests d'intégration pour le nouveau système de stocks
- [ ] **Modifier** : Tests existants pour supprimer les références aux recettes

### **6.2 Tests Frontend**
- [ ] **Créer** : Tests pour les nouveaux composants
- [ ] **Modifier** : Tests existants pour le nouveau système

### **6.3 Tests d'Intégration**
- [ ] **Tester** : Flux complet d'import
- [ ] **Tester** : Déduction automatique lors des commandes
- [ ] **Tester** : Génération d'alertes
- [ ] **Tester** : Calcul des coûts en temps réel

---

## 📅 Planning de Migration

### **Semaine 1 : Nettoyage**
- Phase 1 complète (suppression ancien système)
- Migration de la base de données

### **Semaine 2 : Backend**
- Phase 2.1 (nouveaux services backend)
- Tests unitaires backend

### **Semaine 3 : Frontend**
- Phase 2.2 (nouveaux services frontend)
- Phase 3 (intégration interfaces)

### **Semaine 4 : POS et Rapports**
- Phase 4 (intégration POS)
- Phase 5 (tableaux de bord)

### **Semaine 5 : Tests et Déploiement**
- Phase 6 (tests complets)
- Déploiement et formation

---

## 📁 Fichiers à Créer/Modifier - Checklist Détaillée

### **Backend - Nouveaux Fichiers**
- [ ] `backend/controllers/produitIngredient.controller.js`
- [ ] `backend/routes/produitIngredient.routes.js`
- [ ] `backend/services/menuIngredientImport.service.js`
- [ ] `backend/schema/migrations/cleanup_old_recipe_system.sql`

### **Backend - Fichiers à Modifier**
- [ ] `backend/services/posStockIntegration.service.js`
- [ ] `backend/services/inventaire.service.js`
- [ ] `backend/services/ingredientImport.service.js`
- [ ] `backend/services/menuImport.service.js`
- [ ] `backend/controllers/posStock.controller.js`
- [ ] `backend/controllers/inventaire.controller.js`
- [ ] `backend/routes/posStock.routes.js`
- [ ] `backend/routes/index.js`

### **Backend - Fichiers à Supprimer**
- [ ] `backend/services/recette.service.js`
- [ ] `backend/controllers/recette.controller.js`
- [ ] `backend/routes/recette.routes.js`

### **Frontend - Nouveaux Fichiers**
- [ ] `hotel-frontend/src/services/produitIngredient.service.ts`
- [ ] `hotel-frontend/src/services/menuIntegre.service.ts`
- [ ] `hotel-frontend/src/components/restaurant/PlatComposition.tsx`
- [ ] `hotel-frontend/src/components/restaurant/MenuStockStatus.tsx`
- [ ] `hotel-frontend/src/components/bar/CocktailComposition.tsx`
- [ ] `hotel-frontend/src/components/bar/BarStockStatus.tsx`
- [ ] `hotel-frontend/src/pages/InventaireDashboard.tsx`
- [ ] `hotel-frontend/src/components/inventaire/RapportConsommation.tsx`
- [ ] `hotel-frontend/src/pages/ManagerDashboard.tsx`
- [ ] `hotel-frontend/src/components/rapports/RapportRentabilite.tsx`
- [ ] `hotel-frontend/src/components/rapports/RapportOperationnel.tsx`

### **Frontend - Fichiers à Modifier**
- [ ] `hotel-frontend/src/services/inventaire.service.ts`
- [ ] `hotel-frontend/src/pages/RestaurantMenuPage.tsx`
- [ ] `hotel-frontend/src/pages/BarMenuPage.tsx`
- [ ] `hotel-frontend/src/components/ui/StockManager.tsx`
- [ ] `hotel-frontend/src/components/pos/ProductSelection.tsx`
- [ ] `hotel-frontend/src/components/pos/CommandeForm.tsx`
- [ ] `hotel-frontend/src/components/pos/CaisseInterface.tsx`

### **Frontend - Fichiers à Supprimer**
- [ ] `hotel-frontend/src/services/recette.service.ts`
- [ ] Tous les composants contenant "recette" dans le nom
- [ ] Pages de gestion des recettes

---

## ⚠️ Points d'Attention

### **Sauvegarde des Données**
- [ ] Sauvegarder les données existantes avant suppression
- [ ] Plan de rollback en cas de problème
- [ ] Export des recettes existantes si nécessaire

### **Formation des Utilisateurs**
- [ ] Documentation du nouveau système
- [ ] Formation sur les nouveaux workflows
- [ ] Guide de migration pour les utilisateurs

### **Performance**
- [ ] Optimisation des requêtes SQL
- [ ] Cache pour les calculs de coûts
- [ ] Index sur les nouvelles tables
- [ ] Monitoring des performances

### **Sécurité**
- [ ] Validation des permissions sur les nouvelles routes
- [ ] Audit des accès aux données sensibles
- [ ] Tests de sécurité sur les nouveaux endpoints

---

## 🎯 Résultat Final

Un système unifié et simplifié où :
- ✅ **Menu** = Liste des produits avec prix de vente
- ✅ **Ingrédients** = Stocks gérés automatiquement
- ✅ **Liens directs** = Produit ↔ Ingrédients (quantités)
- ✅ **Déduction automatique** = Lors des ventes POS
- ✅ **Alertes intelligentes** = Stock faible/rupture
- ✅ **Rapports complets** = Rentabilité et opérationnels
- ✅ **Interface unifiée** = Bar, Restaurant, Inventaire

### **Bénéfices Attendus**
- 🚀 **Simplicité** : Fini les recettes complexes
- 📊 **Transparence** : Coûts calculés en temps réel
- ⚡ **Efficacité** : Gestion automatisée des stocks
- 💰 **Rentabilité** : Suivi précis des marges
- 🔔 **Proactivité** : Alertes avant rupture
- 📈 **Évolutivité** : Base solide pour futures fonctionnalités
