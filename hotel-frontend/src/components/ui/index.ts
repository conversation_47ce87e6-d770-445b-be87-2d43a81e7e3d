// Export de tous les composants UI pour le système d'inventaire Excel

export { FileUploadZone } from './FileUploadZone';
export { ImportPreview } from './ImportPreview';
export { ImportStepper } from './ImportStepper';
export { IngredientModal } from './IngredientModal';
export { IngredientManager } from './IngredientManager';
export { RecetteModal } from './RecetteModal';
export { RecetteManager } from './RecetteManager';
export { StockManager } from './StockManager';
export { UploadStep, ProcessingStep, CompleteStep } from './UploadSteps';

// Nouveaux composants Phase 7
export { InventaireAnalytics } from './InventaireAnalytics';
export { ServiceRecettesManager } from './ServiceRecettesManager';
export { DataTable } from './DataTable';
export { ErrorList } from './ErrorList';
export { SuggestionList } from './SuggestionList';
export { RecetteDetailsModal } from './RecetteDetailsModal';

// Composants d'intégration Phase 5
export { default as QuickLinks } from './QuickLinks';
export { default as StockAlertsWidget } from './StockAlertsWidget';

// Composants d'accès et permissions
export { ComplexeAccessGuard, ComplexeAccessMessage } from './ComplexeAccessGuard';

// Composants d'upload Phase 4 - Import Menu Simplifié
export {
  FileDropzone,
  ImportProgress,
  ImportReport,
  MenuUploader,
  IngredientUploader
} from '../upload';
