import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { authService } from '../../services/auth.service';
import { ContextualErrorPages } from '../errors/ContextualErrorPages';
import { routingMiddleware } from '../../middleware/routingMiddleware';

// Import des pages
import Login from '../../pages/Login';
import Dashboard from '../../pages/Dashboard';
import Reception from '../../pages/Reception';
import POS from '../../pages/POS';
import POSManagement from '../../pages/POSManagement';
import Pool from '../../pages/Pool';
import Kitchen from '../../pages/Kitchen';
import Inventory from '../../pages/Inventory';
import Reports from '../../pages/Reports';
import Services from '../../pages/Services';
import EmployeeManagement from '../../pages/EmployeeManagement';
import ClientReservation from '../../pages/ClientReservation';
import Chambres from '../../pages/Chambres';
import { ReservationConfirmation } from '../../pages/ReservationConfirmation';
import ComplexesList from '../../pages/patron/ComplexesList';
import { AnonymousReservationPage } from '../../pages/AnonymousReservation';

// Import des pages d'inventaire
import { InventairePage } from '../../pages/InventairePage';
import { ImportExcelPage } from '../../pages/ImportExcelPage';
import { ImportHistoryPage } from '../../pages/ImportHistoryPage';
import { ImportDetailsPage } from '../../pages/ImportDetailsPage';
import { TemplatesPage } from '../../pages/TemplatesPage';
import { MenuSetup } from '../../pages/MenuSetup';

// Import des guards
import { AdminAccessGuard } from '../guards/AdminAccessGuard';
import { EmployeeAccessGuard } from '../guards/EmployeeAccessGuard';
import { MultiTypeGuard, ServiceGuard, ConditionalGuard } from '../guards/RouteGuards';

interface ProtectedRouterProps {
  children?: React.ReactNode;
}

// Composant pour gérer la redirection intelligente
const IntelligentRedirect: React.FC = () => {
  const { isAdmin, isEmployee, employeeType, loading } = useEmployeePermissions();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (loading) return;

    // Vérifier l'authentification
    const checkAuthAndRedirect = async () => {
      const isAuthenticated = await authService.isAuthenticated();
      
      if (!isAuthenticated) {
        navigate('/login');
        return;
      }

      // Redirection intelligente selon le type d'utilisateur
      if (isAdmin) {
        // Admin : redirection selon le type et la sélection de complexe
        if (location.pathname === '/' || location.pathname === '/login') {
          // Pour les admins de chaîne, vérifier s'ils ont sélectionné un complexe
          if (authService.isAdminChaine() && authService.needsComplexeSelection()) {
            navigate('/patron/complexes');
          } else {
            navigate('/dashboard');
          }
        }
      } else if (isEmployee && employeeType) {
        // Employé : redirection vers l'interface appropriée
        if (location.pathname === '/' || location.pathname === '/login') {
          switch (employeeType) {
            case 'reception':
              navigate('/reception');
              break;
            case 'gerant_piscine':
              navigate('/pool');
              break;
            case 'serveuse':
            case 'gerant_services':
              navigate('/pos');
              break;
            case 'cuisine':
              navigate('/kitchen');
              break;
            default:
              navigate('/dashboard');
          }
        }
      } else {
        // Type d'utilisateur non reconnu
        navigate('/errors/403');
      }
    };

    checkAuthAndRedirect();
  }, [isAdmin, isEmployee, employeeType, loading, navigate, location.pathname]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  return null;
};

// Composant wrapper pour appliquer le middleware de routing
const RouteWrapper: React.FC<{ children: React.ReactNode; path: string }> = ({ children, path }) => {
  const { isAdmin, isEmployee, employeeType } = useEmployeePermissions();

  useEffect(() => {
    // Appliquer le middleware de routing
    routingMiddleware.logAccess(path, { isAdmin, isEmployee, employeeType });
  }, [path, isAdmin, isEmployee, employeeType]);

  return <>{children}</>;
};

export const ProtectedRouter: React.FC<ProtectedRouterProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialiser le middleware de routing
    routingMiddleware.initialize();
    setIsInitialized(true);
  }, []);

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Initialisation...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Routes publiques */}
        <Route
          path="/login"
          element={
            <RouteWrapper path="/login">
              <Login />
            </RouteWrapper>
          }
        />
        <Route
          path="/reservation-anonyme"
          element={
            <RouteWrapper path="/reservation-anonyme">
              <AnonymousReservationPage />
            </RouteWrapper>
          }
        />

        {/* Routes d'erreur */}
        <Route 
          path="/errors/403" 
          element={
            <RouteWrapper path="/errors/403">
              <ContextualErrorPages type="403" />
            </RouteWrapper>
          } 
        />
        <Route 
          path="/errors/404" 
          element={
            <RouteWrapper path="/errors/404">
              <ContextualErrorPages type="404" />
            </RouteWrapper>
          } 
        />
        <Route 
          path="/errors/500" 
          element={
            <RouteWrapper path="/errors/500">
              <ContextualErrorPages type="500" />
            </RouteWrapper>
          } 
        />

        {/* Routes pour la sélection de complexe */}
        <Route
          path="/patron/complexes"
          element={
            <RouteWrapper path="/patron/complexes">
              <ConditionalGuard condition={(permissions) => permissions.isAdmin}>
                <ComplexesList />
              </ConditionalGuard>
            </RouteWrapper>
          }
        />
        {/* Route de redirection pour compatibilité */}
        <Route
          path="/complexes"
          element={<Navigate to="/patron/complexes" replace />}
        />

        {/* Dashboard - Accessible à tous les utilisateurs authentifiés */}
        <Route 
          path="/dashboard" 
          element={
            <RouteWrapper path="/dashboard">
              <MultiTypeGuard allowedTypes={['admin', 'employee']}>
                <Dashboard />
              </MultiTypeGuard>
            </RouteWrapper>
          } 
        />

        {/* Routes Admin */}
        <Route 
          path="/reports" 
          element={
            <RouteWrapper path="/reports">
              <AdminAccessGuard requiredPage="reports">
                <Reports />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/employee-management" 
          element={
            <RouteWrapper path="/employee-management">
              <AdminAccessGuard>
                <EmployeeManagement />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route
          path="/services"
          element={
            <RouteWrapper path="/services">
              <MultiTypeGuard allowedTypes={['admin', 'employee']}>
                <Services />
              </MultiTypeGuard>
            </RouteWrapper>
          }
        />
        <Route
          path="/menu-setup"
          element={
            <RouteWrapper path="/menu-setup">
              <AdminAccessGuard>
                <MenuSetup />
              </AdminAccessGuard>
            </RouteWrapper>
          }
        />
        <Route 
          path="/inventory" 
          element={
            <RouteWrapper path="/inventory">
              <AdminAccessGuard>
                <Inventory />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/pos-management" 
          element={
            <RouteWrapper path="/pos-management">
              <AdminAccessGuard>
                <POSManagement />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />

        {/* Routes Employé Opérationnelles */}
        <Route 
          path="/reception" 
          element={
            <RouteWrapper path="/reception">
              <EmployeeAccessGuard requiredType="reception" allowAdmin={true}>
                <Reception />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/pool" 
          element={
            <RouteWrapper path="/pool">
              <EmployeeAccessGuard requiredType="gerant_piscine" allowAdmin={true}>
                <Pool />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/pos" 
          element={
            <RouteWrapper path="/pos">
              <EmployeeAccessGuard requiredType={["serveuse", "gerant_services", "cuisine"]} allowAdmin={true}>
                <POS />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/kitchen" 
          element={
            <RouteWrapper path="/kitchen">
              <EmployeeAccessGuard requiredType="cuisine" allowAdmin={true}>
                <Kitchen />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />

        {/* Routes Mixtes (Admin + Employé spécifique) */}
        <Route 
          path="/client-reservations" 
          element={
            <RouteWrapper path="/client-reservations">
              <EmployeeAccessGuard requiredType="reception" allowAdmin={true}>
                <ClientReservation />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/chambres" 
          element={
            <RouteWrapper path="/chambres">
              <EmployeeAccessGuard requiredType="reception" allowAdmin={true}>
                <Chambres />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/reservation-confirmation" 
          element={
            <RouteWrapper path="/reservation-confirmation">
              <EmployeeAccessGuard requiredType="reception" allowAdmin={true}>
                <ReservationConfirmation />
              </EmployeeAccessGuard>
            </RouteWrapper>
          } 
        />

        {/* Routes d'inventaire Excel - Admin uniquement */}
        <Route 
          path="/inventaire" 
          element={
            <RouteWrapper path="/inventaire">
              <AdminAccessGuard>
                <InventairePage />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/import-excel" 
          element={
            <RouteWrapper path="/import-excel">
              <AdminAccessGuard>
                <ImportExcelPage />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/import-history" 
          element={
            <RouteWrapper path="/import-history">
              <AdminAccessGuard>
                <ImportHistoryPage />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/import-details/:importId" 
          element={
            <RouteWrapper path="/import-details/:importId">
              <AdminAccessGuard>
                <ImportDetailsPage />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />
        <Route 
          path="/templates" 
          element={
            <RouteWrapper path="/templates">
              <AdminAccessGuard>
                <TemplatesPage />
              </AdminAccessGuard>
            </RouteWrapper>
          } 
        />

        {/* Route par défaut avec redirection intelligente */}
        <Route path="/" element={<IntelligentRedirect />} />

        {/* Route 404 pour toutes les autres URLs */}
        <Route 
          path="*" 
          element={
            <RouteWrapper path="*">
              <ContextualErrorPages type="404" />
            </RouteWrapper>
          } 
        />
      </Routes>
      {children}
    </Router>
  );
};

export default ProtectedRouter;
